'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import Icon from '@/components/ui/icon'
import MediaProcessor from './MediaProcessor'
import ProcessingProgress from './ProcessingProgress'
import { useBatchFFmpeg } from '@/hooks/useFFmpeg'
import { formatFileSize, getMediaType, type MediaProcessingResult } from '@/types/media'

export default function MediaProcessorPage() {
  const [results, setResults] = useState<Array<{ result: MediaProcessingResult; filename: string }>>([])
  const batchProcessor = useBatchFFmpeg()

  const handleResult = (result: MediaProcessingResult, filename: string) => {
    setResults(prev => [...prev, { result, filename }])
  }

  const clearResults = () => {
    setResults([])
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold flex items-center gap-3">
          <Icon type="settings" size="lg" />
          媒体处理工具
        </h1>
        <p className="text-gray-600 mt-2">
          使用 FFmpeg.wasm 在浏览器中处理视频和音频文件
        </p>
      </div>

      <Tabs defaultValue="single" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="single">单文件处理</TabsTrigger>
          <TabsTrigger value="batch">批量处理</TabsTrigger>
        </TabsList>

        {/* 单文件处理 */}
        <TabsContent value="single" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 主处理区域 */}
            <div className="lg:col-span-2">
              <MediaProcessor onResult={handleResult} />
            </div>

            {/* 侧边栏 - 状态和结果 */}
            <div className="space-y-6">
              {/* FFmpeg 状态 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Icon type="cpu" size="sm" />
                    FFmpeg 状态
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">加载状态:</span>
                      <Badge variant={batchProcessor.ffmpeg.loadState.isLoaded ? "default" : "outline"}>
                        {batchProcessor.ffmpeg.loadState.isLoaded ? "已加载" : "未加载"}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm">处理状态:</span>
                      <Badge variant={
                        batchProcessor.ffmpeg.processingStatus === 'processing' ? "default" :
                        batchProcessor.ffmpeg.processingStatus === 'completed' ? "secondary" :
                        batchProcessor.ffmpeg.processingStatus === 'error' ? "destructive" : "outline"
                      }>
                        {batchProcessor.ffmpeg.processingStatus === 'idle' && "空闲"}
                        {batchProcessor.ffmpeg.processingStatus === 'loading' && "加载中"}
                        {batchProcessor.ffmpeg.processingStatus === 'processing' && "处理中"}
                        {batchProcessor.ffmpeg.processingStatus === 'completed' && "已完成"}
                        {batchProcessor.ffmpeg.processingStatus === 'error' && "错误"}
                      </Badge>
                    </div>

                    {!batchProcessor.ffmpeg.loadState.isLoaded && (
                      <Button 
                        onClick={() => batchProcessor.ffmpeg.load()} 
                        size="sm" 
                        className="w-full"
                        disabled={batchProcessor.ffmpeg.loadState.isLoading}
                      >
                        {batchProcessor.ffmpeg.loadState.isLoading ? (
                          <>
                            <Icon type="loader" size="xs" className="mr-2 animate-spin" />
                            加载中...
                          </>
                        ) : (
                          <>
                            <Icon type="download" size="xs" className="mr-2" />
                            加载 FFmpeg
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* 处理进度 */}
              {batchProcessor.ffmpeg.progress && (
                <ProcessingProgress
                  status={batchProcessor.ffmpeg.processingStatus}
                  progress={batchProcessor.ffmpeg.progress}
                />
              )}

              {/* 处理结果 */}
              {results.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <Icon type="check" size="sm" />
                        处理结果 ({results.length})
                      </span>
                      <Button variant="ghost" size="sm" onClick={clearResults}>
                        清空
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {results.map((item, index) => (
                        <div key={index} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium truncate">
                              {item.filename}
                            </span>
                            <Badge variant={item.result.success ? "secondary" : "destructive"}>
                              {item.result.success ? "成功" : "失败"}
                            </Badge>
                          </div>
                          
                          {item.result.success && (
                            <div className="text-xs text-gray-500">
                              大小: {formatFileSize(item.result.size || 0)}
                            </div>
                          )}
                          
                          {item.result.error && (
                            <div className="text-xs text-red-600">
                              {item.result.error}
                            </div>
                          )}
                          
                          {item.result.success && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="w-full mt-2"
                              onClick={() => batchProcessor.ffmpeg.downloadResult(item.result, item.filename)}
                            >
                              <Icon type="download" size="xs" className="mr-1" />
                              下载
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* 批量处理 */}
        <TabsContent value="batch" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon type="layers" size="sm" />
                批量处理
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <Icon type="construction" size="lg" className="mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">批量处理功能</h3>
                <p>此功能正在开发中，敬请期待...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 功能介绍 */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon type="info" size="sm" />
            功能介绍
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Icon type="video" size="sm" />
                视频处理
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 格式转换 (MP4, WebM, AVI 等)</li>
                <li>• 视频压缩和质量调整</li>
                <li>• 视频剪辑和分割</li>
                <li>• 添加文字水印</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Icon type="music" size="sm" />
                音频处理
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 音频格式转换 (MP3, WAV, AAC 等)</li>
                <li>• 从视频提取音频</li>
                <li>• 音频压缩和质量调整</li>
                <li>• 音频剪辑和分割</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Icon type="shield" size="sm" />
                隐私安全
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 完全在浏览器中处理</li>
                <li>• 文件不会上传到服务器</li>
                <li>• 处理完成后自动清理</li>
                <li>• 开源技术，安全可靠</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
