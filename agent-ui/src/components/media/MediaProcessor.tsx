'use client'

import { useState, useCallback, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from 'sonner'
import Icon from '@/components/ui/icon'
import { useFFmpeg } from '@/hooks/useFFmpeg'
import { 
  getMediaType, 
  getRecommendedFormats, 
  formatFileSize, 
  formatDuration,
  DEFAULT_PRESETS,
  type MediaType,
  type ProcessingPreset,
  type MediaProcessingResult
} from '@/types/media'

interface MediaProcessorProps {
  onResult?: (result: MediaProcessingResult, filename: string) => void
  className?: string
}

export default function MediaProcessor({ onResult, className }: MediaProcessorProps) {
  // 状态
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [selectedPreset, setSelectedPreset] = useState<ProcessingPreset | null>(null)
  const [customFormat, setCustomFormat] = useState<string>('')
  const [result, setResult] = useState<MediaProcessingResult | null>(null)
  
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // FFmpeg Hook
  const ffmpeg = useFFmpeg()

  // 文件选择处理
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      setResult(null)
      ffmpeg.clearLogs()
      toast.success(`已选择文件: ${file.name}`)
    }
  }, [ffmpeg])

  // 拖拽处理
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    if (file) {
      setSelectedFile(file)
      setResult(null)
      ffmpeg.clearLogs()
      toast.success(`已选择文件: ${file.name}`)
    }
  }, [ffmpeg])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
  }, [])

  // 开始处理
  const handleProcess = useCallback(async () => {
    if (!selectedFile) {
      toast.error('请先选择文件')
      return
    }

    try {
      // 确保 FFmpeg 已加载
      if (!ffmpeg.loadState.isLoaded) {
        toast.loading('正在加载 FFmpeg...')
        await ffmpeg.load()
        toast.dismiss()
      }

      let processingResult: MediaProcessingResult

      if (selectedPreset) {
        // 使用预设
        processingResult = await ffmpeg.processMedia(
          selectedFile,
          selectedPreset.type,
          selectedPreset.options
        )
      } else if (customFormat) {
        // 使用自定义格式
        const mediaType = getMediaType(selectedFile)
        processingResult = await ffmpeg.processMedia(
          selectedFile,
          'convert',
          { format: customFormat as any }
        )
      } else {
        toast.error('请选择处理方式')
        return
      }

      setResult(processingResult)

      if (processingResult.success) {
        toast.success('处理完成！')
        
        // 生成输出文件名
        const extension = selectedPreset?.options && 'format' in selectedPreset.options 
          ? selectedPreset.options.format 
          : customFormat
        const outputFilename = `processed_${selectedFile.name.split('.')[0]}.${extension}`
        
        onResult?.(processingResult, outputFilename)
      } else {
        const errorMsg = processingResult.error || '未知错误'
        console.error('Media processing failed:', processingResult)

        // 根据错误类型提供更具体的提示
        if (errorMsg.includes('FFmpeg 未加载')) {
          toast.error('FFmpeg 未就绪，请刷新页面重试')
        } else if (errorMsg.includes('not supported')) {
          toast.error('不支持的文件格式，请选择 MP4、WebM、AVI、MOV 或 MKV 格式')
        } else if (errorMsg.includes('timeout')) {
          toast.error('处理超时，请尝试较小的文件或重试')
        } else {
          toast.error(`处理失败: ${errorMsg}`)
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      console.error('Media processing error:', error)

      // 根据错误类型提供更具体的提示
      if (errorMessage.includes('FFmpeg 未加载')) {
        toast.error('FFmpeg 未就绪，请刷新页面重试')
      } else if (errorMessage.includes('not supported')) {
        toast.error('不支持的文件格式，请选择支持的视频格式')
      } else if (errorMessage.includes('timeout')) {
        toast.error('处理超时，请尝试较小的文件或重试')
      } else {
        toast.error(`处理失败: ${errorMessage}`)
      }
    }
  }, [selectedFile, selectedPreset, customFormat, ffmpeg, onResult])

  // 下载结果
  const handleDownload = useCallback(() => {
    if (!result || !selectedFile) return

    const extension = selectedPreset?.options && 'format' in selectedPreset.options 
      ? selectedPreset.options.format 
      : customFormat
    const filename = `processed_${selectedFile.name.split('.')[0]}.${extension}`
    
    ffmpeg.downloadResult(result, filename)
  }, [result, selectedFile, selectedPreset, customFormat, ffmpeg])

  // 重置
  const handleReset = useCallback(() => {
    setSelectedFile(null)
    setSelectedPreset(null)
    setCustomFormat('')
    setResult(null)
    ffmpeg.clearLogs()
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [ffmpeg])

  // 获取适用的预设
  const getApplicablePresets = useCallback((mediaType: MediaType) => {
    return DEFAULT_PRESETS.filter(preset => 
      preset.mediaType === mediaType || 
      (preset.mediaType === 'video' && mediaType === 'video')
    )
  }, [])

  const mediaType = selectedFile ? getMediaType(selectedFile) : null
  const applicablePresets = mediaType ? getApplicablePresets(mediaType) : []
  const recommendedFormats = mediaType ? getRecommendedFormats(mediaType) : []

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 文件上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon type="upload" size="sm" />
            选择媒体文件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*,audio/*"
              onChange={handleFileSelect}
              className="hidden"
            />
            
            {selectedFile ? (
              <div className="space-y-2">
                <Icon type="file" size="lg" className="mx-auto text-green-500" />
                <p className="font-medium">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {formatFileSize(selectedFile.size)} • {selectedFile.type}
                </p>
                <Badge variant="outline">{mediaType}</Badge>
              </div>
            ) : (
              <div className="space-y-2">
                <Icon type="upload" size="lg" className="mx-auto text-gray-400" />
                <p className="text-lg font-medium">拖拽文件到此处或点击选择</p>
                <p className="text-sm text-gray-500">支持视频和音频文件</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 处理选项 */}
      {selectedFile && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon type="settings" size="sm" />
              处理选项
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="presets" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="presets">预设配置</TabsTrigger>
                <TabsTrigger value="custom">自定义</TabsTrigger>
              </TabsList>
              
              <TabsContent value="presets" className="space-y-4">
                <div className="grid gap-3">
                  {applicablePresets.map((preset) => (
                    <div
                      key={preset.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedPreset?.id === preset.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => {
                        setSelectedPreset(preset)
                        setCustomFormat('')
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{preset.name}</h4>
                          <p className="text-sm text-gray-500">{preset.description}</p>
                        </div>
                        <Badge variant="secondary">{preset.type}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="custom" className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">输出格式</label>
                  <Select value={customFormat} onValueChange={(value) => {
                    setCustomFormat(value)
                    setSelectedPreset(null)
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择输出格式" />
                    </SelectTrigger>
                    <SelectContent>
                      {recommendedFormats.map((format) => (
                        <SelectItem key={format} value={format}>
                          {format.toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* 控制按钮 */}
      {selectedFile && (
        <div className="flex gap-3">
          <Button
            onClick={handleProcess}
            disabled={ffmpeg.processingStatus === 'processing' || (!selectedPreset && !customFormat)}
            className="flex-1"
          >
            {ffmpeg.processingStatus === 'processing' ? (
              <>
                <Icon type="loader" size="sm" className="mr-2 animate-spin" />
                处理中...
              </>
            ) : (
              <>
                <Icon type="play" size="sm" className="mr-2" />
                开始处理
              </>
            )}
          </Button>
          
          <Button variant="outline" onClick={handleReset}>
            <Icon type="refresh" size="sm" className="mr-2" />
            重置
          </Button>
        </div>
      )}

      {/* 进度显示 */}
      {ffmpeg.progress && ffmpeg.processingStatus === 'processing' && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>处理进度</span>
                <span>{(ffmpeg.progress.progress * 100).toFixed(1)}%</span>
              </div>
              <Progress value={ffmpeg.progress.progress * 100} />
              <p className="text-xs text-gray-500">
                已处理时间: {(ffmpeg.progress.time / 1000000).toFixed(2)} 秒
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 结果显示 */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon type={result.success ? "check" : "x"} size="sm" />
              处理结果
            </CardTitle>
          </CardHeader>
          <CardContent>
            {result.success ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-green-600">处理成功</p>
                    <p className="text-sm text-gray-500">
                      文件大小: {formatFileSize(result.size || 0)}
                    </p>
                    {result.processingTime && (
                      <p className="text-sm text-gray-500">
                        处理时间: {(result.processingTime / 1000).toFixed(2)} 秒
                      </p>
                    )}
                  </div>
                  <Button onClick={handleDownload}>
                    <Icon type="download" size="sm" className="mr-2" />
                    下载
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-red-600">
                <p className="font-medium">处理失败</p>
                <p className="text-sm">{result.error}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 日志显示 */}
      {ffmpeg.logs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Icon type="terminal" size="sm" />
                处理日志
              </span>
              <Button variant="ghost" size="sm" onClick={ffmpeg.clearLogs}>
                清空
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-900 text-gray-100 p-4 rounded-lg max-h-48 overflow-y-auto font-mono text-sm">
              {ffmpeg.logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
