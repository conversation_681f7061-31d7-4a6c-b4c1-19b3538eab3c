'use client'

import { useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { toast } from 'sonner'
import Icon from '@/components/ui/icon'
import MediaProcessor from './MediaProcessor'
import type { MediaProcessingResult } from '@/types/media'

interface MediaProcessButtonProps {
  mediaUrl: string
  mediaType: 'video' | 'audio'
  filename?: string
  className?: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
}

export default function MediaProcessButton({
  mediaUrl,
  mediaType,
  filename,
  className,
  variant = 'outline',
  size = 'sm'
}: MediaProcessButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isConverting, setIsConverting] = useState(false)

  // 从 URL 创建 File 对象
  const createFileFromUrl = useCallback(async (url: string, filename: string): Promise<File> => {
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error('Failed to fetch media file')
      }
      
      const blob = await response.blob()
      return new File([blob], filename, { type: blob.type })
    } catch (error) {
      console.error('Error creating file from URL:', error)
      throw error
    }
  }, [])

  // 处理结果
  const handleResult = useCallback((result: MediaProcessingResult, outputFilename: string) => {
    if (result.success) {
      toast.success(`处理完成: ${outputFilename}`)
      setIsOpen(false)
    } else {
      toast.error(`处理失败: ${result.error}`)
    }
  }, [])

  // 快速转换功能
  const quickConvert = useCallback(async (format: string) => {
    if (isConverting) return

    setIsConverting(true)
    
    try {
      // 动态导入 FFmpeg hook
      const { useFFmpeg } = await import('@/hooks/useFFmpeg')
      
      // 这里需要在组件外部使用 hook，所以我们直接使用服务
      const { ffmpegService, ffmpegOperations } = await import('@/lib/ffmpeg')
      
      toast.loading('正在准备文件...')
      
      // 从 URL 创建文件
      const file = await createFileFromUrl(mediaUrl, filename || `media.${mediaType}`)
      
      toast.dismiss()
      toast.loading('正在加载 FFmpeg...')
      
      // 确保 FFmpeg 已加载
      if (!ffmpegService.isFFmpegLoaded()) {
        await ffmpegService.load()
      }
      
      toast.dismiss()
      toast.loading(`正在转换为 ${format.toUpperCase()}...`)
      
      // 执行转换
      let result: Uint8Array
      if (mediaType === 'video') {
        result = await ffmpegOperations.convertVideo(file, format)
      } else {
        result = await ffmpegOperations.convertAudio(file, format)
      }
      
      // 下载结果
      const outputFilename = `converted_${file.name.split('.')[0]}.${format}`
      const blob = new Blob([result])
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = outputFilename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      toast.dismiss()
      toast.success(`转换完成: ${outputFilename}`)
      
    } catch (error) {
      console.error('Quick convert error:', error)
      toast.dismiss()
      toast.error(`转换失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsConverting(false)
    }
  }, [mediaUrl, mediaType, filename, isConverting, createFileFromUrl])

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <Icon type="settings" size="xs" className="mr-1" />
          处理
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icon type="settings" size="sm" />
            媒体处理工具
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* 快速操作 */}
          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Icon type="zap" size="sm" />
              快速操作
            </h3>
            
            <div className="flex flex-wrap gap-2">
              {mediaType === 'video' && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => quickConvert('mp4')}
                    disabled={isConverting}
                  >
                    {isConverting ? (
                      <Icon type="loader" size="xs" className="mr-1 animate-spin" />
                    ) : (
                      <Icon type="video" size="xs" className="mr-1" />
                    )}
                    转为 MP4
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => quickConvert('webm')}
                    disabled={isConverting}
                  >
                    {isConverting ? (
                      <Icon type="loader" size="xs" className="mr-1 animate-spin" />
                    ) : (
                      <Icon type="video" size="xs" className="mr-1" />
                    )}
                    转为 WebM
                  </Button>
                </>
              )}
              
              {mediaType === 'audio' && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => quickConvert('mp3')}
                    disabled={isConverting}
                  >
                    {isConverting ? (
                      <Icon type="loader" size="xs" className="mr-1 animate-spin" />
                    ) : (
                      <Icon type="music" size="xs" className="mr-1" />
                    )}
                    转为 MP3
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => quickConvert('wav')}
                    disabled={isConverting}
                  >
                    {isConverting ? (
                      <Icon type="loader" size="xs" className="mr-1 animate-spin" />
                    ) : (
                      <Icon type="music" size="xs" className="mr-1" />
                    )}
                    转为 WAV
                  </Button>
                </>
              )}
              
              {mediaType === 'video' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => quickConvert('mp3')}
                  disabled={isConverting}
                >
                  {isConverting ? (
                    <Icon type="loader" size="xs" className="mr-1 animate-spin" />
                  ) : (
                    <Icon type="music" size="xs" className="mr-1" />
                  )}
                  提取音频
                </Button>
              )}
            </div>
          </div>
          
          {/* 高级处理 */}
          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Icon type="settings" size="sm" />
              高级处理
            </h3>
            
            <MediaProcessorWrapper
              mediaUrl={mediaUrl}
              mediaType={mediaType}
              filename={filename}
              onResult={handleResult}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// 包装组件，用于处理异步文件加载
function MediaProcessorWrapper({
  mediaUrl,
  mediaType,
  filename,
  onResult
}: {
  mediaUrl: string
  mediaType: 'video' | 'audio'
  filename?: string
  onResult: (result: MediaProcessingResult, filename: string) => void
}) {
  const [file, setFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const loadFile = useCallback(async () => {
    if (file || isLoading) return

    setIsLoading(true)
    try {
      const response = await fetch(mediaUrl)
      if (!response.ok) {
        throw new Error('Failed to fetch media file')
      }
      
      const blob = await response.blob()
      const loadedFile = new File([blob], filename || `media.${mediaType}`, { type: blob.type })
      setFile(loadedFile)
    } catch (error) {
      console.error('Error loading file:', error)
      toast.error('加载文件失败')
    } finally {
      setIsLoading(false)
    }
  }, [mediaUrl, mediaType, filename, file, isLoading])

  if (!file && !isLoading) {
    return (
      <div className="text-center py-8">
        <Button onClick={loadFile}>
          <Icon type="download" size="sm" className="mr-2" />
          加载文件进行处理
        </Button>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <Icon type="loader" size="lg" className="animate-spin mx-auto mb-2" />
        <p>正在加载文件...</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        已加载文件: {file?.name}
      </div>
      <MediaProcessor onResult={onResult} />
    </div>
  )
}
