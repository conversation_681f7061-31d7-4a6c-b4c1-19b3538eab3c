'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/ui/icon'
import { formatDuration } from '@/types/media'
import type { ProcessingProgress, ProcessingStatus } from '@/types/media'

interface ProcessingProgressProps {
  status: ProcessingStatus
  progress?: ProcessingProgress | null
  filename?: string
  className?: string
}

export default function ProcessingProgressComponent({ 
  status, 
  progress, 
  filename,
  className 
}: ProcessingProgressProps) {
  const getStatusIcon = (status: ProcessingStatus) => {
    switch (status) {
      case 'loading':
        return <Icon type="loader" size="sm" className="animate-spin" />
      case 'processing':
        return <Icon type="play" size="sm" />
      case 'completed':
        return <Icon type="check" size="sm" color="positive" />
      case 'error':
        return <Icon type="x" size="sm" color="destructive" />
      default:
        return <Icon type="clock" size="sm" />
    }
  }

  const getStatusBadge = (status: ProcessingStatus) => {
    switch (status) {
      case 'loading':
        return <Badge variant="secondary">加载中</Badge>
      case 'processing':
        return <Badge variant="default">处理中</Badge>
      case 'completed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">完成</Badge>
      case 'error':
        return <Badge variant="destructive">错误</Badge>
      default:
        return <Badge variant="outline">等待中</Badge>
    }
  }

  const getStatusText = (status: ProcessingStatus) => {
    switch (status) {
      case 'loading':
        return '正在加载 FFmpeg，请稍候...'
      case 'processing':
        return '正在处理媒体文件，请耐心等待...'
      case 'completed':
        return '处理完成！可以开始下一步操作'
      case 'error':
        return '处理失败，请检查文件格式或重试'
      default:
        return '等待开始处理'
    }
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-lg">
          <div className="flex items-center gap-2">
            {getStatusIcon(status)}
            处理进度
          </div>
          {getStatusBadge(status)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {filename && (
          <div className="text-sm text-gray-600">
            <span className="font-medium">文件:</span> {filename}
          </div>
        )}
        
        <div className="text-sm text-gray-600">
          {getStatusText(status)}
        </div>

        {progress && status === 'processing' && (
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>进度</span>
              <span className="font-medium">{(progress.progress * 100).toFixed(1)}%</span>
            </div>
            
            <Progress value={progress.progress * 100} className="h-2" />
            
            <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
              <div>
                <span className="font-medium">已处理时间:</span>
                <br />
                {formatDuration(progress.time / 1000000)}
              </div>
              
              {progress.speed && (
                <div>
                  <span className="font-medium">处理速度:</span>
                  <br />
                  {progress.speed.toFixed(2)}x
                </div>
              )}
            </div>

            {progress.eta && (
              <div className="text-xs text-gray-500">
                <span className="font-medium">预计剩余时间:</span> {formatDuration(progress.eta)}
              </div>
            )}
          </div>
        )}

        {status === 'completed' && (
          <div className="flex items-center gap-2 text-sm text-green-600">
            <Icon type="check" size="sm" />
            处理成功完成
          </div>
        )}

        {status === 'error' && (
          <div className="flex items-center gap-2 text-sm text-red-600">
            <Icon type="x" size="sm" />
            处理过程中发生错误
          </div>
        )}
      </CardContent>
    </Card>
  )
}
