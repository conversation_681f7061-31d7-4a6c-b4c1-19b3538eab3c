'use client'

import { useCallback, useRef, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/ui/icon'
import { motion, AnimatePresence } from 'framer-motion'
import { toast } from 'sonner'
import { 
  type NarrationMode, 
  type VideoFileInfo, 
  type ProcessingState,
  formatFileSize,
  generateId,
  getNarrationModeConfig
} from '@/types/narration'

interface VideoUploaderProps {
  mode: NarrationMode
  onFilesUploaded: (files: VideoFileInfo[]) => void
  onProcessingStateChange: (state: ProcessingState) => void
  className?: string
}

export default function VideoUploader({
  mode,
  onFilesUploaded,
  onProcessingStateChange,
  className
}: VideoUploaderProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<VideoFileInfo[]>([])
  const [processingState, setProcessingState] = useState<ProcessingState>({
    isLoading: false,
    isProcessing: false,
    progress: 0,
    currentStep: ''
  })
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const modeConfig = getNarrationModeConfig(mode)

  // 更新处理状态
  const updateProcessingState = useCallback((state: Partial<ProcessingState>) => {
    const newState = { ...processingState, ...state }
    setProcessingState(newState)
    onProcessingStateChange(newState)
  }, [processingState, onProcessingStateChange])

  // 验证文件类型
  const validateFile = useCallback((file: File): boolean => {
    const validTypes = ['video/mp4', 'video/webm', 'video/avi', 'video/mov', 'video/mkv']
    if (!validTypes.includes(file.type)) {
      toast.error(`不支持的文件格式: ${file.type}`)
      return false
    }
    
    // 文件大小限制 (500MB)
    const maxSize = 500 * 1024 * 1024
    if (file.size > maxSize) {
      toast.error('文件大小不能超过 500MB')
      return false
    }
    
    return true
  }, [])

  // 处理文件选择
  const handleFiles = useCallback((files: FileList) => {
    const fileArray = Array.from(files)
    
    // 单文件模式检查
    if (!modeConfig.supportMultipleFiles && fileArray.length > 1) {
      toast.error(`${modeConfig.name}模式只支持单个文件上传`)
      return
    }
    
    // 验证所有文件
    const validFiles = fileArray.filter(validateFile)
    if (validFiles.length === 0) {
      return
    }
    
    // 创建文件信息
    const videoFiles: VideoFileInfo[] = validFiles.map((file, index) => ({
      id: generateId(),
      file,
      name: file.name,
      size: file.size,
      order: uploadedFiles.length + index + 1,
      status: 'pending'
    }))
    
    const newFiles = [...uploadedFiles, ...videoFiles]
    setUploadedFiles(newFiles)
    onFilesUploaded(newFiles)
    
    toast.success(`成功添加 ${validFiles.length} 个视频文件`)
  }, [modeConfig, uploadedFiles, validateFile, onFilesUploaded])

  // 拖拽处理
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFiles(files)
    }
  }, [handleFiles])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  // 文件选择
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      handleFiles(files)
    }
  }, [handleFiles])

  // 移除文件
  const removeFile = useCallback((fileId: string) => {
    const newFiles = uploadedFiles.filter(f => f.id !== fileId)
    setUploadedFiles(newFiles)
    onFilesUploaded(newFiles)
    toast.success('文件已移除')
  }, [uploadedFiles, onFilesUploaded])

  // 清空所有文件
  const clearAllFiles = useCallback(() => {
    setUploadedFiles([])
    onFilesUploaded([])
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    toast.success('已清空所有文件')
  }, [onFilesUploaded])

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 上传区域 */}
      <Card>
        <CardContent className="p-6">
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 cursor-pointer ${
              isDragOver
                ? 'border-blue-500 bg-blue-50/50 dark:bg-blue-950/20'
                : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50/50 dark:hover:bg-gray-800/50'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              multiple={modeConfig.supportMultipleFiles}
              onChange={handleFileSelect}
              className="hidden"
            />
            
            <motion.div
              animate={{ scale: isDragOver ? 1.05 : 1 }}
              transition={{ duration: 0.2 }}
              className="space-y-4"
            >
              <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center ${
                isDragOver
                  ? 'bg-blue-100 dark:bg-blue-900/50'
                  : 'bg-gray-100 dark:bg-gray-800'
              }`}>
                <Icon
                  type="upload"
                  size="lg"
                  color={isDragOver ? 'brand' : 'muted'}
                />
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">
                  上传视频，开始您的影视解说之旅
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                  {modeConfig.supportMultipleFiles 
                    ? '拖拽多个视频文件到此处或点击选择文件'
                    : '拖拽视频文件到此处或点击选择文件'
                  }
                </p>
                
                <div className="flex flex-wrap justify-center gap-2 text-xs text-gray-400">
                  <Badge variant="outline">MP4</Badge>
                  <Badge variant="outline">WebM</Badge>
                  <Badge variant="outline">AVI</Badge>
                  <Badge variant="outline">MOV</Badge>
                  <Badge variant="outline">MKV</Badge>
                </div>
              </div>
            </motion.div>
          </div>
        </CardContent>
      </Card>

      {/* 已上传文件列表 */}
      <AnimatePresence>
        {uploadedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium">已选择的文件 ({uploadedFiles.length})</h4>
                  <Button variant="ghost" size="sm" onClick={clearAllFiles}>
                    <Icon type="trash" size="xs" className="mr-1" />
                    清空
                  </Button>
                </div>
                
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {uploadedFiles.map((file) => (
                    <motion.div
                      key={file.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      className="flex items-center justify-between p-3 bg-gray-100 dark:bg-gray-700 rounded-lg"
                    >
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <Icon type="video" size="sm" color="brand" className="flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          <p className="text-sm font-medium truncate text-gray-900 dark:text-gray-100">{file.name}</p>
                          <p className="text-xs text-gray-600 dark:text-gray-300">
                            {formatFileSize(file.size)}
                            {modeConfig.supportMultipleFiles && (
                              <span className="ml-2">序号: {file.order}</span>
                            )}
                          </p>
                        </div>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        className="flex-shrink-0"
                      >
                        <Icon type="x" size="xs" />
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 处理进度 */}
      <AnimatePresence>
        {(processingState.isLoading || processingState.isProcessing) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Icon type="loader" size="sm" className="animate-spin" />
                    <span className="font-medium">{processingState.currentStep}</span>
                  </div>
                  
                  {processingState.progress > 0 && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>处理进度</span>
                        <span>{processingState.progress.toFixed(1)}%</span>
                      </div>
                      <Progress value={processingState.progress} />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
