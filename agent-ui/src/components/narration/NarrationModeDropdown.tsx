'use client'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/ui/icon'
import { motion } from 'framer-motion'
import { NARRATION_MODES, type NarrationMode } from '@/types/narration'

interface NarrationModeDropdownProps {
  selectedMode: NarrationMode
  onModeChange: (mode: NarrationMode) => void
  className?: string
}

export default function NarrationModeDropdown({
  selectedMode,
  onModeChange,
  className
}: NarrationModeDropdownProps) {
  const selectedModeConfig = NARRATION_MODES.find(m => m.id === selectedMode)

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="text-xs font-medium uppercase text-primary/80 tracking-wider">
        解说模式
      </div>

      <Select value={selectedMode} onValueChange={onModeChange}>
        <SelectTrigger className="w-full h-auto p-4 border-accent/50 bg-background/50 hover:bg-background/80 hover:border-accent transition-all duration-200">
          <SelectValue>
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-brand/10 border border-brand/20">
                <Icon
                  type={selectedModeConfig?.icon as any}
                  size="sm"
                  color="brand"
                />
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium text-primary text-sm">
                  {selectedModeConfig?.name}
                </div>
                <div className="text-xs text-muted mt-0.5">
                  {selectedModeConfig?.description}
                </div>
              </div>
            </div>
          </SelectValue>
        </SelectTrigger>

        <SelectContent className="w-full min-w-[280px] border-accent/50 bg-background/95 backdrop-blur-sm">
          {NARRATION_MODES.map((mode) => (
            <SelectItem
              key={mode.id}
              value={mode.id}
              className="p-4 cursor-pointer hover:bg-accent/50 focus:bg-accent/50 data-[highlighted]:bg-accent/50"
            >
              <motion.div
                className="flex items-center gap-3 w-full"
                whileHover={{ x: 2 }}
                transition={{ duration: 0.2 }}
              >
                <div className={`p-2 rounded-lg border transition-all duration-200 ${
                  selectedMode === mode.id
                    ? 'bg-brand/20 border-brand/30'
                    : 'bg-accent/30 border-accent/50'
                }`}>
                  <Icon
                    type={mode.icon as any}
                    size="sm"
                    color={selectedMode === mode.id ? 'brand' : 'muted'}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-primary text-sm">
                      {mode.name}
                    </span>
                    <div className="flex gap-1">
                      {mode.supportMultipleFiles && (
                        <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-auto border-brand/30 text-brand/80">
                          多文件
                        </Badge>
                      )}
                      {mode.supportMerge && (
                        <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-auto border-positive/30 text-positive/80">
                          合并
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="text-xs text-muted leading-relaxed">
                    {mode.description}
                  </div>
                </div>
              </motion.div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* 优化的模式说明 */}
      <motion.div
        className="text-xs bg-accent/30 border border-accent/50 rounded-lg p-3"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-start gap-2">
          <Icon type="info" size="xs" color="brand" className="mt-0.5 flex-shrink-0" />
          <div className="text-muted">
            {selectedMode === 'short-drama' ? (
              <span>支持多视频上传、拖拽排序和一键合并功能</span>
            ) : selectedMode === 'movie' ? (
              <span>单视频上传，适合完整电影的解说制作</span>
            ) : selectedMode === 'tv-series' ? (
              <span>单视频上传，适合电视剧集的解说制作</span>
            ) : (
              <span>单视频上传，适合纪录片的解说制作</span>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  )
}
